<?php
/**
 * Theme for the Postlight Headless WordPress Starter Kit.
 *
 * Read more about this project at:
 * https://postlight.com/trackchanges/introducing-postlights-wordpress-react-starter-kit
 *
 * @package  Postlight_Headless_WP
 */

// Frontend origin.
require_once 'inc/frontend-origin.php';

// ACF commands.
require_once 'inc/class-acf-commands.php';

// Logging functions.
require_once 'inc/log.php';

// CORS handling.
require_once 'inc/cors.php';

// Admin modifications.
require_once 'inc/admin.php';

// Add Menus.
require_once 'inc/menus.php';

// Add Headless Settings area.
require_once 'inc/acf-options.php';

// Add GraphQL resolvers.
require_once 'inc/graphql/resolvers.php';

require_once 'inc/rest.php';

// Add more support for image size customization
add_theme_support( 'post-thumbnails' );
add_image_size( 'sm_400', 400, 9999 ); // Unlimited Height Mode
add_image_size( 'md_800', 800, 9999 ); // Unlimited Height Mode
add_image_size( '1x_1200', 1200, 9999 ); // Unlimited Height Mode
add_image_size( '2x_1800', 1800, 9999 ); // Unlimited Height Mode

// change WordPress API URL to HOME URL
add_filter('rest_url', 'ex_home_url_as_api_url');
function ex_home_url_as_api_url($url) {
	$url = str_replace(home_url(),site_url() , $url);
	return $url;
}

add_filter('home_url', 'ex_force_home_url', 9999, 2);

function ex_force_home_url($home_url, $path) {
	if( !str_contains(get_site_url(), 'staging') ) {
		$home_url = str_replace("johntest.exercise", "www.exercise", $home_url);
		$home_url = str_replace("staging.exercise", "www.exercise", $home_url);
		$home_url = str_replace("cms.exercise", "www.exercise", $home_url);

	}

//	echo $home_url;
	return $home_url;
}

add_filter( 'option_home', function( $home_url ){
	if( !str_contains(get_site_url(), 'staging') ) {
		$home_url = str_replace("johntest.exercise", "www.exercise", $home_url);
		$home_url = str_replace("staging.exercise", "www.exercise", $home_url);
		$home_url = str_replace("cms.exercise", "www.exercise", $home_url);
	}
	return $home_url;
});

// ACF Blocks for Gutenberg
add_action('acf/init', 'my_acf_init');
function my_acf_init() {
    
    // check function exists
    if( function_exists('acf_register_block') ) {
        
        // register a testimonial block
        acf_register_block(array(
            'name'              => 'testimonial',
            'title'             => __('Testimonial'),
            'description'       => __('A custom testimonial block that will display full width (landscape) when displayed on desktop.'),
            'render_callback'   => 'my_acf_block_render_callback',
            'category'          => 'formatting',
            'icon'              => 'admin-comments',
            'keywords'          => array( 'testimonial', 'quote' ),
        ));
    }
}

function my_acf_block_render_callback( $block ) {
    
	// convert name ("acf/testimonial") into path friendly slug ("testimonial")
	$slug = str_replace('acf/', '', $block['name']);
	
	// include a template part from within the "template-parts/block" folder
	if( file_exists( get_theme_file_path("/template-parts/block/content-testimonial.php") ) ) {
			include( get_theme_file_path("/template-parts/block/content-testimonial.php") );
	}
}

function custom_rest_api_check_post_type($response, $post, $request) {
    // Check the post type
     $post_type = get_post_type($post);

     // Add the post type to the API response
     $response->data['content']['rendered'] = $response->data['content']['rendered'] . do_shortcode('[rp4wp]');
     return $response;
}
add_filter('rest_prepare_alternative', 'custom_rest_api_check_post_type', 10, 3);
add_filter('rest_prepare_grow', 'custom_rest_api_check_post_type', 10, 3);
add_filter('rest_prepare_comparison', 'custom_rest_api_check_post_type', 10, 3);


/* Current Year Stuff */
function title_set_current_year($title) {
    return str_replace('#current_year', date("Y"), $title);
}
add_filter('the_title', 'title_set_current_year');
add_filter('rp4wp_post_title', 'title_set_current_year');
add_filter('get_the_archive_title', 'title_set_current_year');

/**
 * #current_month and #current_year tag Replacement on breadcrumbs
 */
function aioseo_breadcrumbs_current_month_year_replacemnets($crumbs) {
    foreach ($crumbs as $key => $crumb) {
        $crumb['label'] = str_replace(['#current_month', '#current_year'], [date("F"), date("Y")], $crumb['label']);
        $crumbs[$key] = $crumb;
    }
    return $crumbs;
}
add_filter('aioseo_breadcrumbs_trail', 'aioseo_breadcrumbs_current_month_year_replacemnets');

function customize_header_field_value($value, $post_id, $field) {
    return str_replace('#current_year', date("Y"), $value);
}

// Hook into the acf/format_value filter
add_filter('acf/format_value/key=field_6376e42087dc4_field_61b98a1b01513', 'customize_header_field_value', 10, 3);

function phoenix_remove_current_year_from_slug($post_id) {
    // verify post is not a revision
    if (!wp_is_post_revision($post_id)) {
        $post = get_post($post_id);
        if ($post instanceof WP_Post) {
            // check if smart tag is in the post title
            $count = preg_match("/#current_year/", $post->post_title);
            if ($count === 1) {
                // get new slug without current_year in it
                $count = 0;
                $slug = $post->post_name;
                $new_slug = str_replace("-current_year", "", $slug, $count);

                // update slug if we made changes
                if ($count !== 0) {
                    // unhook this function to prevent infinite looping
                    remove_action('save_post', 'phoenix_remove_current_year_from_slug');
                    // update the post slug
                    wp_update_post([
                        'ID' => $post_id,
                        'post_name' => $new_slug
                    ]);
                    // re-hook this function
                    add_action('save_post', 'phoenix_remove_current_year_from_slug');
                }
            }
        }
    }
}
add_action('save_post', 'phoenix_remove_current_year_from_slug');


// Updated at 05/08/2025
function wp_headless_preview_link($link, $post) {
    $map = [
        'page'           => '',
        'post'           => '/blog',
        'grow'           => '/grow',
        'alternative'    => '/grow/alternative',
        'solutions'      => '/solutions',
        'platform'       => '/platform',
        'support'        => '/support',
        'topics'         => '/support/topics',
        'comparison'     => '/grow/comparison',
        'thank-you'      => '/thank-you',
        'land'           => '/demo',
        'designed_page'  => '',
    ];

    $base = isset($map[$post->post_type])
        ? rtrim($map[$post->post_type], '/')
        : $post->post_type; // fallback

    if ($post->post_type === 'designed_page' && $post->post_name === 'pricing') {
        $base = '/pricing';
    }

    $nameOrID = $post->post_name ?: $post->ID;
    $slug = $base . '/' . $nameOrID;

    $preview = add_query_arg([
        'secret' => 'df98a4e217cf417ca6be9b6ea76d3fc08c0ad7d1d0833fd64395e7fcda893c8e',
        'slug'   => $slug,
    ], 'https://nextjs.staging.exercise.com/api/preview');
    return $preview;
}
add_filter('preview_post_link', 'wp_headless_preview_link', 10, 2);