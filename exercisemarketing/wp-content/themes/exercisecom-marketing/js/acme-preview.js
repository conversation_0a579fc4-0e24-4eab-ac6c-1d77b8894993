/**
 * ACME Preview JavaScript
 * 
 * Handles collection and storage of preview data when user clicks preview button.
 */

(function($) {
    'use strict';

    /**
     * ACME Preview Handler
     */
    var ACMEPreview = {
        
        /**
         * Initialize the preview handler
         */
        init: function() {
            this.bindEvents();
            console.log('ACME Preview initialized');
        },

        /**
         * Bind events
         */
        bindEvents: function() {
            // Hook into the preview button click
            $(document).on('click', '#preview-action a, .preview', this.handlePreviewClick.bind(this));
            
            // Also hook into form submission for preview
            $('#post').on('submit', function(e) {
                var $submitButton = $('input[type="submit"]:focus, button[type="submit"]:focus');
                if ($submitButton.attr('name') === 'wp-preview' || $submitButton.hasClass('preview')) {
                    ACMEPreview.collectAndSavePreviewData();
                }
            });
        },

        /**
         * Handle preview button click
         */
        handlePreviewClick: function(e) {
            console.log('Preview button clicked');
            this.collectAndSavePreviewData();
        },

        /**
         * Collect all form data and save to transient
         */
        collectAndSavePreviewData: function() {
            if (!acmePreview.postId) {
                console.error('ACME Preview: No post ID available');
                return;
            }

            var previewData = this.collectFormData();
            
            console.log('ACME Preview: Collected data', previewData);

            // Send data to server
            $.ajax({
                url: acmePreview.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'acme_save_preview_data',
                    nonce: acmePreview.nonce,
                    post_id: acmePreview.postId,
                    post_title: previewData.post_title,
                    post_content: previewData.post_content,
                    post_excerpt: previewData.post_excerpt,
                    post_status: previewData.post_status,
                    acf: previewData.acf
                },
                success: function(response) {
                    if (response.success) {
                        console.log('ACME Preview: Data saved successfully', response.data);
                    } else {
                        console.error('ACME Preview: Failed to save data', response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('ACME Preview: AJAX error', error);
                }
            });
        },

        /**
         * Collect form data from the post editor
         */
        collectFormData: function() {
            var data = {
                post_title: '',
                post_content: '',
                post_excerpt: '',
                post_status: 'draft',
                acf: {}
            };

            // Get post title
            var $title = $('#title, #post-title-0');
            if ($title.length) {
                data.post_title = $title.val() || '';
            }

            // Get post content - handle both classic and block editor
            data.post_content = this.getPostContent();

            // Get post excerpt
            var $excerpt = $('#excerpt');
            if ($excerpt.length) {
                data.post_excerpt = $excerpt.val() || '';
            }

            // Get post status
            var $status = $('#post_status');
            if ($status.length) {
                data.post_status = $status.val() || 'draft';
            }

            // Get ACF fields
            data.acf = this.collectACFFields();

            return data;
        },

        /**
         * Get post content from editor
         */
        getPostContent: function() {
            var content = '';

            // Try Gutenberg/Block Editor first
            if (typeof wp !== 'undefined' && wp.data && wp.data.select('core/editor')) {
                try {
                    content = wp.data.select('core/editor').getEditedPostContent();
                    console.log('ACME Preview: Got content from Gutenberg editor');
                    return content;
                } catch (e) {
                    console.log('ACME Preview: Gutenberg editor not available, trying classic editor');
                }
            }

            // Try TinyMCE (Classic Editor)
            if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                content = tinyMCE.get('content').getContent();
                console.log('ACME Preview: Got content from TinyMCE editor');
                return content;
            }

            // Fallback to textarea
            var $content = $('#content');
            if ($content.length) {
                content = $content.val() || '';
                console.log('ACME Preview: Got content from textarea');
                return content;
            }

            console.log('ACME Preview: No content editor found');
            return content;
        },

        /**
         * Collect ACF field values
         */
        collectACFFields: function() {
            var acfData = {};

            // Find all ACF fields in the form
            $('input[name^="acf["], select[name^="acf["], textarea[name^="acf["]').each(function() {
                var $field = $(this);
                var name = $field.attr('name');
                var value = $field.val();

                // Extract field key from name attribute
                // Format: acf[field_123456789abcdef] or acf[field_123456789abcdef][subfield]
                var matches = name.match(/acf\[([^\]]+)\]/);
                if (matches && matches[1]) {
                    var fieldKey = matches[1];

                    // Handle different field types
                    if ($field.attr('type') === 'checkbox') {
                        if (!acfData[fieldKey]) {
                            acfData[fieldKey] = [];
                        }
                        if ($field.is(':checked')) {
                            acfData[fieldKey].push(value);
                        }
                    } else if ($field.attr('type') === 'radio') {
                        if ($field.is(':checked')) {
                            acfData[fieldKey] = value;
                        }
                    } else {
                        // Handle sub-fields (repeater, group, etc.)
                        var subMatches = name.match(/acf\[([^\]]+)\]\[([^\]]+)\]/);
                        if (subMatches && subMatches[2]) {
                            if (!acfData[fieldKey]) {
                                acfData[fieldKey] = {};
                            }
                            acfData[fieldKey][subMatches[2]] = value;
                        } else {
                            acfData[fieldKey] = value;
                        }
                    }
                }
            });

            // Handle ACF file fields and other special fields
            $('.acf-field').each(function() {
                var $field = $(this);
                var fieldKey = $field.data('key');
                var fieldType = $field.data('type');

                if (!fieldKey) return;

                switch (fieldType) {
                    case 'image':
                    case 'file':
                        var $hiddenInput = $field.find('input[type="hidden"]');
                        if ($hiddenInput.length) {
                            acfData[fieldKey] = $hiddenInput.val();
                        }
                        break;

                    case 'wysiwyg':
                        var editorId = $field.find('.wp-editor-area').attr('id');
                        if (editorId && typeof tinyMCE !== 'undefined' && tinyMCE.get(editorId)) {
                            acfData[fieldKey] = tinyMCE.get(editorId).getContent();
                        }
                        break;

                    case 'true_false':
                        var $checkbox = $field.find('input[type="checkbox"]');
                        if ($checkbox.length) {
                            acfData[fieldKey] = $checkbox.is(':checked') ? 1 : 0;
                        }
                        break;
                }
            });

            console.log('ACME Preview: Collected ACF fields', acfData);
            return acfData;
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        ACMEPreview.init();
    });

    // Also initialize when Gutenberg is ready
    if (typeof wp !== 'undefined' && wp.domReady) {
        wp.domReady(function() {
            ACMEPreview.init();
        });
    }

})(jQuery);
