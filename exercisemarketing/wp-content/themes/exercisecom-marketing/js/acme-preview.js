/**
 * ACME Preview JavaScript
 *
 * Handles collection and storage of preview data when user clicks preview button.
 */

jQuery(function($) {
    'use strict';

    console.log('%c[ACME Preview Script] Loaded and running', 'color: blue; font-weight: bold;');

    /**
     * Opens preview in new tab with custom handling
     */
    function openPreviewTab(url) {
        window.open(url, '_blank', 'noopener,noreferrer');
    }

        /**
         * Replace preview links with custom buttons
         */
        replaceLinksWithButtons: function() {
            console.log('[Debug] Starting replaceLinksWithButtons function...');

            var selectors = [
                'a[href*="/api/preview/"]',
                '.editor-preview-dropdown__button-external',
                'a[target="wp-preview-' + acmePreview.postId + '"]',
                'a[aria-label="View post"]',
                'a[aria-label="View page"]',
                '#post-preview'
            ];

            var selectorString = selectors.join(', ');
            console.log('[Debug] Using selector:', selectorString);

            var $previewLinks = $(selectorString);

            console.log('[Debug] Found preview links:', $previewLinks.length);
            console.log('[Debug] Preview links elements:', $previewLinks.toArray());

            if ($previewLinks.length === 0) {
                console.log('[Debug] No preview links found. Checking individual selectors:');
                selectors.forEach(function(selector, index) {
                    var $elements = $(selector);
                    console.log('[Debug] Selector ' + (index + 1) + ' "' + selector + '" found:', $elements.length, 'elements');
                    if ($elements.length > 0) {
                        $elements.each(function(i, el) {
                            console.log('[Debug] Element ' + (i + 1) + ':', {
                                tagName: el.tagName,
                                href: el.href,
                                target: el.target,
                                className: el.className,
                                innerHTML: el.innerHTML
                            });
                        });
                    }
                });

                var $allPreviewLinks = $('a[href*="preview"], a[target*="preview"]');
                console.log('[Debug] All links containing "preview":', $allPreviewLinks.length);
                $allPreviewLinks.each(function(i, el) {
                    console.log('[Debug] Preview link ' + (i + 1) + ':', {
                        href: el.href,
                        target: el.target,
                        className: el.className,
                        text: $(el).text().trim()
                    });
                });
            }

            var self = this;
            $previewLinks.each(function(index, element) {
                var $link = $(element);
                var originalHref = $link.attr('href');
                var linkText = $link.text() || $link.html();
                var linkClasses = $link.attr('class') || '';
                var linkId = $link.attr('id') || '';

                console.log('[Debug] Processing link ' + (index + 1) + ':', {
                    originalHref: originalHref,
                    linkText: linkText,
                    linkClasses: linkClasses,
                    linkId: linkId,
                    element: element
                });

                if ($link.data('replaced-with-button')) {
                    console.log('[Debug] Link already replaced, skipping...');
                    return;
                }

                var $button = $('<button></button>')
                    .addClass(linkClasses)
                    .html(linkText)
                    .attr('type', 'button')
                    .data('original-href', originalHref)
                    .data('replaced-with-button', true);

                if (linkId) {
                    $button.attr('id', linkId + '-button');
                }

                console.log('[Debug] Created button:', {
                    classes: $button.attr('class'),
                    originalHref: $button.data('original-href'),
                    html: $button.html()
                });

                $button.on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    var buttonOriginalHref = $(this).data('original-href');
                    console.log('[Preview Button] Button clicked (replaced from link):', buttonOriginalHref);

                    // Collect and save preview data, then open preview
                    self.submitPreviewData().then(function(success) {
                        if (success) {
                            console.log('[Preview Button] Preview data saved, opening preview');
                            self.openPreviewTab(buttonOriginalHref);
                        } else {
                            console.error('[Preview Button] Failed to save preview data');
                            // Still open preview with original data
                            self.openPreviewTab(buttonOriginalHref);
                        }
                    }).catch(function(error) {
                        console.error('[Error] Failed to submit preview data for button:', error);
                        self.openPreviewTab(buttonOriginalHref);
                    });
                });

                $link.replaceWith($button);
                console.log('[Debug] Successfully replaced link with button');
            });

            console.log('[Debug] replaceLinksWithButtons function completed');
        },

        /**
         * Setup MutationObserver for dynamic content
         */
        setupMutationObserver: function() {
            if (!window.MutationObserver) {
                console.log("Init MutationObserver failed");
                return;
            }

            var self = this;
            var observer = new MutationObserver(function(mutations) {
                var shouldReplace = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        $(mutation.addedNodes).each(function() {
                            if (this.nodeType === 1) { // Element node
                                var $node = $(this);
                                var selectorHeader = '.editor-post-preview, .editor-header__post-preview-button, .components-button.is-compact.has-icon[aria-label*="View"], #post-preview';
                                var selectorDropdown = 'a[href*="/api/preview/"], .editor-preview-dropdown__button-external, a[target*="wp-preview"]';

                                if ($node.is(selectorHeader) ||
                                    $node.find(selectorHeader).length > 0 ||
                                    $node.is(selectorDropdown) ||
                                    $node.find(selectorDropdown).length > 0) {
                                    console.log("Find Node:", $node);
                                    shouldReplace = true;
                                    return false; // break out of each loop
                                }
                            }
                        });
                    }
                });

                if (shouldReplace) {
                    console.log('[MutationObserver] New preview links detected, replacing...');
                    setTimeout(function() {
                        self.replaceLinksWithButtons();
                    }, 100);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            console.log('[Init] MutationObserver set up for dynamic content');
        },

        /**
         * Submit preview data and return promise
         */
        submitPreviewData: function() {
            if (!acmePreview.postId) {
                console.error('ACME Preview: No post ID available');
                return Promise.reject('No post ID available');
            }

            var previewData = this.collectFormData();

            console.log('ACME Preview: Collected data', previewData);

            return new Promise(function(resolve, reject) {
                // Send data to server
                $.ajax({
                    url: acmePreview.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'acme_save_preview_data',
                        nonce: acmePreview.nonce,
                        post_id: acmePreview.postId,
                        post_title: previewData.post_title,
                        post_content: previewData.post_content,
                        post_excerpt: previewData.post_excerpt,
                        post_status: previewData.post_status,
                        acf: previewData.acf
                    },
                    success: function(response) {
                        if (response.success) {
                            console.log('ACME Preview: Data saved successfully', response.data);
                            resolve(true);
                        } else {
                            console.error('ACME Preview: Failed to save data', response.data);
                            reject(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('ACME Preview: AJAX error', error);
                        reject(error);
                    }
                });
            });
        },

        /**
         * Collect form data from the post editor
         */
        collectFormData: function() {
            var data = {
                post_title: '',
                post_content: '',
                post_excerpt: '',
                post_status: 'draft',
                acf: {}
            };

            // Get post title
            var $title = $('#title, #post-title-0');
            if ($title.length) {
                data.post_title = $title.val() || '';
            }

            // Get post content - handle both classic and block editor
            data.post_content = this.getPostContent();

            // Get post excerpt
            var $excerpt = $('#excerpt');
            if ($excerpt.length) {
                data.post_excerpt = $excerpt.val() || '';
            }

            // Get post status
            var $status = $('#post_status');
            if ($status.length) {
                data.post_status = $status.val() || 'draft';
            }

            // Get ACF fields
            data.acf = this.collectACFFields();

            return data;
        },

        /**
         * Get post content from editor
         */
        getPostContent: function() {
            var content = '';

            // Try Gutenberg/Block Editor first
            if (typeof wp !== 'undefined' && wp.data && wp.data.select('core/editor')) {
                try {
                    content = wp.data.select('core/editor').getEditedPostContent();
                    console.log('ACME Preview: Got content from Gutenberg editor');
                    return content;
                } catch (e) {
                    console.log('ACME Preview: Gutenberg editor not available, trying classic editor');
                }
            }

            // Try TinyMCE (Classic Editor)
            if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
                content = tinyMCE.get('content').getContent();
                console.log('ACME Preview: Got content from TinyMCE editor');
                return content;
            }

            // Fallback to textarea
            var $content = $('#content');
            if ($content.length) {
                content = $content.val() || '';
                console.log('ACME Preview: Got content from textarea');
                return content;
            }

            console.log('ACME Preview: No content editor found');
            return content;
        },

        /**
         * Collect ACF field values
         */
        collectACFFields: function() {
            var acfData = {};

            // Find all ACF fields in the form
            $('input[name^="acf["], select[name^="acf["], textarea[name^="acf["]').each(function() {
                var $field = $(this);
                var name = $field.attr('name');
                var value = $field.val();

                // Extract field key from name attribute
                // Format: acf[field_123456789abcdef] or acf[field_123456789abcdef][subfield]
                var matches = name.match(/acf\[([^\]]+)\]/);
                if (matches && matches[1]) {
                    var fieldKey = matches[1];

                    // Handle different field types
                    if ($field.attr('type') === 'checkbox') {
                        if (!acfData[fieldKey]) {
                            acfData[fieldKey] = [];
                        }
                        if ($field.is(':checked')) {
                            acfData[fieldKey].push(value);
                        }
                    } else if ($field.attr('type') === 'radio') {
                        if ($field.is(':checked')) {
                            acfData[fieldKey] = value;
                        }
                    } else {
                        // Handle sub-fields (repeater, group, etc.)
                        var subMatches = name.match(/acf\[([^\]]+)\]\[([^\]]+)\]/);
                        if (subMatches && subMatches[2]) {
                            if (!acfData[fieldKey]) {
                                acfData[fieldKey] = {};
                            }
                            acfData[fieldKey][subMatches[2]] = value;
                        } else {
                            acfData[fieldKey] = value;
                        }
                    }
                }
            });

            // Handle ACF file fields and other special fields
            $('.acf-field').each(function() {
                var $field = $(this);
                var fieldKey = $field.data('key');
                var fieldType = $field.data('type');

                if (!fieldKey) return;

                switch (fieldType) {
                    case 'image':
                    case 'file':
                        var $hiddenInput = $field.find('input[type="hidden"]');
                        if ($hiddenInput.length) {
                            acfData[fieldKey] = $hiddenInput.val();
                        }
                        break;

                    case 'wysiwyg':
                        var editorId = $field.find('.wp-editor-area').attr('id');
                        if (editorId && typeof tinyMCE !== 'undefined' && tinyMCE.get(editorId)) {
                            acfData[fieldKey] = tinyMCE.get(editorId).getContent();
                        }
                        break;

                    case 'true_false':
                        var $checkbox = $field.find('input[type="checkbox"]');
                        if ($checkbox.length) {
                            acfData[fieldKey] = $checkbox.is(':checked') ? 1 : 0;
                        }
                        break;
                }
            });

            console.log('ACME Preview: Collected ACF fields', acfData);
            return acfData;
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        ACMEPreview.init();
    });

    // Also initialize when Gutenberg is ready
    if (typeof wp !== 'undefined' && wp.domReady) {
        wp.domReady(function() {
            ACMEPreview.init();
        });
    }

    // Re-initialize when editor is fully loaded (for Gutenberg)
    if (typeof wp !== 'undefined' && wp.data && wp.data.subscribe) {
        var unsubscribe = wp.data.subscribe(function() {
            var isEditorReady = wp.data.select('core/editor') &&
                               wp.data.select('core/editor').isCleanNewPost !== undefined;

            if (isEditorReady) {
                setTimeout(function() {
                    ACMEPreview.replaceLinksWithButtons();
                }, 500);
                unsubscribe(); // Only run once
            }
        });
    }

})(jQuery);
