<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ACME Preview API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>ACME Preview API Test Page</h1>
    
    <div class="info test-section">
        <h3>📋 Instructions</h3>
        <p>This page helps you test the ACME Preview API functionality:</p>
        <ol>
            <li>Fill in the form below with test data</li>
            <li>Click "Save Preview Data" to store data in WordPress transient</li>
            <li>Click "Get Preview Data" to retrieve and display the preview via REST API</li>
            <li>Check the console for detailed logs</li>
        </ol>
        <p><strong>Note:</strong> You must be logged into WordPress and have edit permissions for the post.</p>
    </div>

    <div class="test-section">
        <h3>🔧 Configuration</h3>
        <div class="form-group">
            <label for="wordpress-url">WordPress URL:</label>
            <input type="text" id="wordpress-url" value="https://cms.exercise.com" placeholder="https://your-wordpress-site.com">
        </div>
        <div class="form-group">
            <label for="post-id">Post ID:</label>
            <input type="number" id="post-id" value="1" placeholder="Enter post ID">
        </div>
        <div class="form-group">
            <label for="post-type">Post Type:</label>
            <input type="text" id="post-type" value="post" placeholder="post, page, etc.">
        </div>
    </div>

    <div class="test-section">
        <h3>📝 Preview Data</h3>
        <div class="form-group">
            <label for="preview-title">Title:</label>
            <input type="text" id="preview-title" value="Test Preview Title" placeholder="Enter preview title">
        </div>
        <div class="form-group">
            <label for="preview-content">Content:</label>
            <textarea id="preview-content" placeholder="Enter preview content">This is test preview content with <strong>HTML formatting</strong>.</textarea>
        </div>
        <div class="form-group">
            <label for="preview-excerpt">Excerpt:</label>
            <textarea id="preview-excerpt" placeholder="Enter preview excerpt">This is a test excerpt for the preview.</textarea>
        </div>
        <div class="form-group">
            <label for="acf-fields">ACF Fields (JSON):</label>
            <textarea id="acf-fields" placeholder='{"field_name": "field_value"}'>{
  "test_text_field": "Sample ACF text value",
  "test_number_field": "123",
  "test_true_false": "1"
}</textarea>
        </div>
    </div>

    <div class="test-section">
        <h3>🚀 Actions</h3>
        <button onclick="savePreviewData()">💾 Save Preview Data</button>
        <button onclick="getPreviewData()">📥 Get Preview Data</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>

    <div id="results" class="test-section" style="display: none;">
        <h3>📊 Results</h3>
        <div id="result-content"></div>
    </div>

    <script>
        function getWordPressUrl() {
            return document.getElementById('wordpress-url').value.replace(/\/$/, '');
        }

        function getPostId() {
            return parseInt(document.getElementById('post-id').value);
        }

        function getPostType() {
            return document.getElementById('post-type').value;
        }

        function showResult(content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const contentDiv = document.getElementById('result-content');
            
            resultsDiv.style.display = 'block';
            resultsDiv.className = `test-section ${type}`;
            contentDiv.innerHTML = content;
        }

        function clearResults() {
            document.getElementById('results').style.display = 'none';
        }

        async function savePreviewData() {
            console.log('🔄 Starting save preview data...');
            
            const wordpressUrl = getWordPressUrl();
            const postId = getPostId();
            
            let acfFields = {};
            try {
                const acfText = document.getElementById('acf-fields').value.trim();
                if (acfText) {
                    acfFields = JSON.parse(acfText);
                }
            } catch (e) {
                showResult('❌ Invalid ACF JSON format: ' + e.message, 'error');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'acme_save_preview_data');
            formData.append('post_id', postId);
            formData.append('post_title', document.getElementById('preview-title').value);
            formData.append('post_content', document.getElementById('preview-content').value);
            formData.append('post_excerpt', document.getElementById('preview-excerpt').value);
            formData.append('post_status', 'draft');
            
            // Add ACF fields
            for (const [key, value] of Object.entries(acfFields)) {
                formData.append(`acf[${key}]`, value);
            }

            // Note: In a real implementation, you'd need to get the nonce from WordPress
            // formData.append('nonce', 'your-nonce-here');

            try {
                const response = await fetch(`${wordpressUrl}/wp-admin/admin-ajax.php`, {
                    method: 'POST',
                    body: formData,
                    credentials: 'include' // Include cookies for authentication
                });

                const result = await response.json();
                console.log('💾 Save response:', result);

                if (result.success) {
                    showResult(`
                        <h4>✅ Preview Data Saved Successfully!</h4>
                        <p><strong>Message:</strong> ${result.data.message}</p>
                        <p><strong>Transient Key:</strong> ${result.data.transient_key}</p>
                        <p><strong>Expires In:</strong> ${result.data.expires_in} seconds</p>
                        ${result.data.data_summary ? `
                        <p><strong>Data Summary:</strong></p>
                        <ul>
                            <li>Title Length: ${result.data.data_summary.title_length}</li>
                            <li>Content Length: ${result.data.data_summary.content_length}</li>
                            <li>ACF Fields: ${result.data.data_summary.acf_fields}</li>
                        </ul>
                        ` : ''}
                    `, 'success');
                } else {
                    showResult(`❌ Failed to save preview data: ${result.data}`, 'error');
                }
            } catch (error) {
                console.error('💥 Save error:', error);
                showResult(`❌ Error saving preview data: ${error.message}`, 'error');
            }
        }

        async function getPreviewData() {
            console.log('🔄 Starting get preview data...');
            
            const wordpressUrl = getWordPressUrl();
            const postId = getPostId();
            const postType = getPostType();

            try {
                const response = await fetch(`${wordpressUrl}/wp-json/acme/v1/preview/${postType}/${postId}`, {
                    method: 'GET',
                    credentials: 'include', // Include cookies for authentication
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                console.log('📥 Get response:', result);

                if (response.ok) {
                    showResult(`
                        <h4>✅ Preview Data Retrieved Successfully!</h4>
                        <p><strong>Post ID:</strong> ${result.id}</p>
                        <p><strong>Title:</strong> ${result.title.rendered}</p>
                        <p><strong>Status:</strong> ${result.status}</p>
                        <p><strong>Type:</strong> ${result.type}</p>
                        ${result.acf ? `<p><strong>ACF Fields:</strong> ${Object.keys(result.acf).length} fields found</p>` : ''}
                        ${result._acme_preview_debug ? `
                        <p><strong>Debug Info:</strong></p>
                        <ul>
                            <li>Transient Key: ${result._acme_preview_debug.transient_key}</li>
                            <li>Preview Timestamp: ${result._acme_preview_debug.preview_timestamp}</li>
                            <li>ACF Fields Count: ${result._acme_preview_debug.acf_fields_count}</li>
                            <li>Generated At: ${result._acme_preview_debug.generated_at}</li>
                        </ul>
                        ` : ''}
                        <details>
                            <summary>📋 Full Response Data</summary>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </details>
                    `, 'success');
                } else {
                    showResult(`❌ Failed to get preview data (${response.status}): ${result.message || result.code}`, 'error');
                }
            } catch (error) {
                console.error('💥 Get error:', error);
                showResult(`❌ Error getting preview data: ${error.message}`, 'error');
            }
        }

        // Initialize
        console.log('🚀 ACME Preview API Test Page loaded');
        console.log('📝 Open browser console to see detailed logs');
    </script>
</body>
</html>
