<?php
/**
 * ACME Preview API for Next.js
 * 
 * Provides preview functionality that matches official WordPress REST API structure
 * for use with Next.js preview mode.
 *
 * @package  Exercisecom_Marketing
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class ACME_Preview_API
 * 
 * Handles preview data storage and REST API endpoints for Next.js preview functionality.
 */
class ACME_Preview_API {

    /**
     * Initialize the preview API
     */
    public static function init() {
        add_action( 'rest_api_init', [ __CLASS__, 'register_rest_routes' ] );
        add_action( 'wp_ajax_acme_save_preview_data', [ __CLASS__, 'save_preview_data' ] );
        add_action( 'admin_enqueue_scripts', [ __CLASS__, 'enqueue_preview_scripts' ] );
        
        // Debug logging
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'ACME Preview API initialized' );
        }
    }

    /**
     * Register REST API routes
     */
    public static function register_rest_routes() {
        register_rest_route( 'acme/v1', '/preview/(?P<type>[a-z0-9_-]+)/(?P<id>\d+)', [
            'methods'             => 'GET',
            'callback'            => [ __CLASS__, 'get_preview_data' ],
            'permission_callback' => [ __CLASS__, 'check_preview_permissions' ],
            'args'                => [
                'type' => [
                    'required'          => true,
                    'sanitize_callback' => 'sanitize_key',
                ],
                'id'   => [
                    'required'          => true,
                    'sanitize_callback' => 'absint',
                ],
            ],
        ] );

        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'ACME Preview REST routes registered' );
        }
    }

    /**
     * Check permissions for preview access
     *
     * @param WP_REST_Request $request The REST request.
     * @return bool|WP_Error
     */
    public static function check_preview_permissions( $request ) {
        $post_id = (int) $request['id'];
        
        // Check if user is logged in
        if ( ! is_user_logged_in() ) {
            if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                error_log( "ACME Preview: Access denied - user not logged in for post {$post_id}" );
            }
            return new WP_Error( 'rest_forbidden', __( 'You must be logged in to access preview data.' ), [ 'status' => 401 ] );
        }

        // Check if user can edit the post
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                error_log( "ACME Preview: Access denied - user cannot edit post {$post_id}" );
            }
            return new WP_Error( 'rest_forbidden', __( 'You do not have permission to preview this content.' ), [ 'status' => 403 ] );
        }

        return true;
    }

    /**
     * Save preview data via AJAX
     */
    public static function save_preview_data() {
        // Verify nonce
        if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'acme_preview_nonce' ) ) {
            wp_die( 'Security check failed' );
        }

        $post_id = absint( $_POST['post_id'] ?? 0 );
        $user_id = get_current_user_id();

        if ( ! $post_id || ! current_user_can( 'edit_post', $post_id ) ) {
            wp_send_json_error( 'Invalid post ID or insufficient permissions' );
        }

        // Collect all preview data
        $preview_data = [
            'post_title'   => sanitize_text_field( $_POST['post_title'] ?? '' ),
            'post_content' => wp_kses_post( $_POST['post_content'] ?? '' ),
            'post_excerpt' => sanitize_textarea_field( $_POST['post_excerpt'] ?? '' ),
            'post_status'  => sanitize_text_field( $_POST['post_status'] ?? 'draft' ),
            'acf_fields'   => [],
            'timestamp'    => current_time( 'timestamp' ),
        ];

        // Collect ACF fields
        if ( isset( $_POST['acf'] ) && is_array( $_POST['acf'] ) ) {
            foreach ( $_POST['acf'] as $field_key => $field_value ) {
                $preview_data['acf_fields'][ sanitize_key( $field_key ) ] = $field_value;
            }
        }

        // Store in transient for 15 minutes
        $transient_key = "acme_preview_{$post_id}_{$user_id}";
        $stored = set_transient( $transient_key, $preview_data, 15 * MINUTE_IN_SECONDS );

        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( "ACME Preview: Saved preview data for post {$post_id}, user {$user_id}. Stored: " . ( $stored ? 'yes' : 'no' ) );
            error_log( "ACME Preview: Data keys - " . implode( ', ', array_keys( $preview_data ) ) );
        }

        if ( $stored ) {
            wp_send_json_success( [
                'message'       => 'Preview data saved successfully',
                'transient_key' => $transient_key,
                'expires_in'    => 15 * MINUTE_IN_SECONDS,
            ] );
        } else {
            wp_send_json_error( 'Failed to save preview data' );
        }
    }

    /**
     * Get preview data and return in official API format
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response|WP_Error
     */
    public static function get_preview_data( $request ) {
        $post_id   = (int) $request['id'];
        $post_type = sanitize_key( $request['type'] );
        $user_id   = get_current_user_id();

        // Get the original post
        $post = get_post( $post_id );
        if ( ! $post || $post->post_type !== $post_type ) {
            if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                error_log( "ACME Preview: Post not found or type mismatch - ID: {$post_id}, Type: {$post_type}" );
            }
            return new WP_Error( 'rest_post_invalid_id', __( 'Invalid post ID or type.' ), [ 'status' => 404 ] );
        }

        // Try to get preview data from transient
        $transient_key = "acme_preview_{$post_id}_{$user_id}";
        $preview_data = get_transient( $transient_key );

        if ( ! $preview_data ) {
            if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                error_log( "ACME Preview: No preview data found for key: {$transient_key}" );
            }
            return new WP_Error( 'rest_no_preview_data', __( 'No preview data available. Please save your changes and try again.' ), [ 'status' => 404 ] );
        }

        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( "ACME Preview: Found preview data for post {$post_id}" );
        }

        // Create a merged post object with preview data
        $merged_post = clone $post;
        $merged_post->post_title   = $preview_data['post_title'] ?: $post->post_title;
        $merged_post->post_content = $preview_data['post_content'] ?: $post->post_content;
        $merged_post->post_excerpt = $preview_data['post_excerpt'] ?: $post->post_excerpt;
        $merged_post->post_status  = $preview_data['post_status'] ?: $post->post_status;

        // Build the response using WordPress REST API structure
        $response_data = self::build_rest_response( $merged_post, $preview_data );

        // Clear the transient after successful retrieval (optional)
        // delete_transient( $transient_key );

        return rest_ensure_response( $response_data );
    }

    /**
     * Build REST API response in official format
     *
     * @param WP_Post $post The post object.
     * @param array   $preview_data The preview data.
     * @return array
     */
    private static function build_rest_response( $post, $preview_data ) {
        // Create a REST request to get the official structure
        $rest_request = new WP_REST_Request( 'GET', "/wp/v2/{$post->post_type}s/{$post->ID}" );
        $rest_request->set_param( 'context', 'edit' );
        $rest_request->set_param( '_embed', '1' );

        // Get the appropriate controller
        $controller = self::get_post_type_controller( $post->post_type );
        
        if ( ! $controller ) {
            if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                error_log( "ACME Preview: No controller found for post type: {$post->post_type}" );
            }
            // Fallback to basic structure
            return self::build_basic_response( $post, $preview_data );
        }

        // Prepare the response using the official controller
        $response = $controller->prepare_item_for_response( $post, $rest_request );
        $response_data = $response->get_data();

        // Override with preview data
        if ( isset( $preview_data['post_title'] ) ) {
            $response_data['title']['rendered'] = apply_filters( 'the_title', $preview_data['post_title'] );
            $response_data['title']['raw'] = $preview_data['post_title'];
        }

        if ( isset( $preview_data['post_content'] ) ) {
            $response_data['content']['rendered'] = apply_filters( 'the_content', $preview_data['post_content'] );
            $response_data['content']['raw'] = $preview_data['post_content'];
        }

        if ( isset( $preview_data['post_excerpt'] ) ) {
            $response_data['excerpt']['rendered'] = apply_filters( 'the_excerpt', $preview_data['post_excerpt'] );
            $response_data['excerpt']['raw'] = $preview_data['post_excerpt'];
        }

        // Add ACF fields
        $response_data = self::add_acf_fields( $response_data, $post, $preview_data );

        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( "ACME Preview: Built response with keys: " . implode( ', ', array_keys( $response_data ) ) );
        }

        return $response_data;
    }

    /**
     * Get the appropriate REST controller for a post type
     *
     * @param string $post_type The post type.
     * @return WP_REST_Posts_Controller|null
     */
    private static function get_post_type_controller( $post_type ) {
        $post_type_object = get_post_type_object( $post_type );
        
        if ( ! $post_type_object || ! $post_type_object->show_in_rest ) {
            return null;
        }

        $controller_class = $post_type_object->rest_controller_class ?: 'WP_REST_Posts_Controller';
        
        if ( class_exists( $controller_class ) ) {
            return new $controller_class( $post_type );
        }

        return null;
    }

    /**
     * Build basic response structure as fallback
     *
     * @param WP_Post $post The post object.
     * @param array   $preview_data The preview data.
     * @return array
     */
    private static function build_basic_response( $post, $preview_data ) {
        $response_data = [
            'id'           => $post->ID,
            'date'         => mysql2date( 'c', $post->post_date, false ),
            'date_gmt'     => mysql2date( 'c', $post->post_date_gmt, false ),
            'guid'         => [ 'rendered' => get_the_guid( $post ) ],
            'modified'     => mysql2date( 'c', $post->post_modified, false ),
            'modified_gmt' => mysql2date( 'c', $post->post_modified_gmt, false ),
            'slug'         => $post->post_name,
            'status'       => $preview_data['post_status'] ?: $post->post_status,
            'type'         => $post->post_type,
            'link'         => get_permalink( $post ),
            'title'        => [
                'rendered' => apply_filters( 'the_title', $preview_data['post_title'] ?: $post->post_title ),
                'raw'      => $preview_data['post_title'] ?: $post->post_title,
            ],
            'content'      => [
                'rendered' => apply_filters( 'the_content', $preview_data['post_content'] ?: $post->post_content ),
                'raw'      => $preview_data['post_content'] ?: $post->post_content,
            ],
            'excerpt'      => [
                'rendered' => apply_filters( 'the_excerpt', $preview_data['post_excerpt'] ?: $post->post_excerpt ),
                'raw'      => $preview_data['post_excerpt'] ?: $post->post_excerpt,
            ],
            'author'       => (int) $post->post_author,
            'featured_media' => (int) get_post_thumbnail_id( $post ),
        ];

        // Add ACF fields
        $response_data = self::add_acf_fields( $response_data, $post, $preview_data );

        return $response_data;
    }

    /**
     * Add ACF fields to response
     *
     * @param array   $response_data The response data.
     * @param WP_Post $post The post object.
     * @param array   $preview_data The preview data.
     * @return array
     */
    private static function add_acf_fields( $response_data, $post, $preview_data ) {
        if ( ! function_exists( 'get_fields' ) ) {
            return $response_data;
        }

        // Check if ACF fields are already included via REST API
        if ( isset( $response_data['acf'] ) ) {
            // Override with preview ACF data if available
            if ( ! empty( $preview_data['acf_fields'] ) ) {
                foreach ( $preview_data['acf_fields'] as $field_key => $field_value ) {
                    $response_data['acf'][ $field_key ] = $field_value;
                }
            }
        } else {
            // Manually add ACF fields
            $acf_fields = get_fields( $post->ID ) ?: [];
            
            // Override with preview data
            if ( ! empty( $preview_data['acf_fields'] ) ) {
                $acf_fields = array_merge( $acf_fields, $preview_data['acf_fields'] );
            }

            if ( ! empty( $acf_fields ) ) {
                $response_data['acf'] = $acf_fields;
            }
        }

        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            $acf_count = isset( $response_data['acf'] ) ? count( $response_data['acf'] ) : 0;
            error_log( "ACME Preview: Added {$acf_count} ACF fields to response" );
        }

        return $response_data;
    }

    /**
     * Enqueue preview scripts for admin
     *
     * @param string $hook The current admin page.
     */
    public static function enqueue_preview_scripts( $hook ) {
        // Only load on post edit screens
        if ( ! in_array( $hook, [ 'post.php', 'post-new.php' ], true ) ) {
            return;
        }

        wp_enqueue_script(
            'acme-preview',
            get_template_directory_uri() . '/js/acme-preview.js',
            [ 'jquery' ],
            '1.0.0',
            true
        );

        wp_localize_script( 'acme-preview', 'acmePreview', [
            'ajaxUrl' => admin_url( 'admin-ajax.php' ),
            'nonce'   => wp_create_nonce( 'acme_preview_nonce' ),
            'postId'  => get_the_ID(),
        ] );
    }
}

// Initialize the preview API
ACME_Preview_API::init();
