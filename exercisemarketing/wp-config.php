<?php /* <PERSON><PERSON><PERSON> KINSTA DEVELOPMENT ENVIRONMENT - DO NOT MODIFY THIS CODE BLOCK */ ?>
<?php if ( !defined('KINSTA_DEV_ENV') ) { define('KINSTA_DEV_ENV', true); /* Kinsta development - don't remove this line */ } ?>
<?php if ( !defined('JETPACK_STAGING_MODE') ) { define('JETPACK_STAGING_MODE', true); /* Kinsta development - don't remove this line */ } ?>
<?php /* END KINSTA DEVELOPMENT ENVIRONMENT - DO NOT MODIFY THIS CODE BLOCK */ ?>
<?php

define( 'WP_CACHE', true ); // Added by WP Rocket
/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the
 * installation. You don't have to use the web site, you can
 * copy this file to "wp-config.php" and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * MySQL settings
 * * Secret keys
 * * Database table prefix
 * * ABSPATH
 *
 * @link https://wordpress.org/support/article/editing-wp-config-php/
 *
 * @package WordPress
 */

// ** MySQL settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define( 'DB_NAME', 'exercise_marketing' );

/** MySQL database username */
define( 'DB_USER', 'root' );

/** MySQL database password */
define( 'DB_PASSWORD', 'LSIHqYhmsGOvkOrn' );

/** MySQL hostname */
define( 'DB_HOST', 'devkinsta_db' );

/** Database Charset to use in creating database tables. */
define( 'DB_CHARSET', 'utf8' );

/** The Database Collate type. Don't change this if in doubt. */
define( 'DB_COLLATE', '' );

/**
 * Authentication Unique Keys and Salts.
 *
 * Change these to different unique phrases!
 * You can generate these using the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}
 * You can change these at any point in time to invalidate all existing cookies. This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',          'N(tp>in,_@;!Rgo|S..UB9|qIh@Rv8kRephk*(f#eIa23D|0OA,]Jti mBcM^drC' );
define( 'SECURE_AUTH_KEY',   'V?W%|S5[4G,U)o]cQ=?l{[Q^95~eM>X`q+W:!JDcmEdQG&65t4yKFuS&qOK=g>Ar' );
define( 'LOGGED_IN_KEY',     '|6w>Bvs5+gd&k8YXfUvib@(n*!gl[qsYCHx-7Ejr{}SG0&s3o_B1o+A((X3wenP9' );
define( 'NONCE_KEY',         '$2zV?P~zuwYLV0S4a{z+^$?UP7KBZdf5},g{C9M<q7r#/G}P>+K[6K*(^GA{_`/#' );
define( 'AUTH_SALT',         'xKh+#cxdfWd(?-,hl//6&J#``8|KbK]z#<cgC+,6x$g1~Qc.<=8BA]4<4zuZSxo.' );
define( 'SECURE_AUTH_SALT',  'l;E{ )rGJB5)m.}x$xi /e|Fo,2+SH`{ua`k-V2</Nf=9kc~U~OnN{H#d;?*hRmv' );
define( 'LOGGED_IN_SALT',    '=@q5l@6f4p+UUoJ?Ze.w2%efU;kOIp-&A;#E{~S!8bmc9z*hl`3vSK3`i!zilf.I' );
define( 'NONCE_SALT',        's,jGu(6k8(J]%:;24~QkL*+|&we+TWZ{1Ef&Ku#(+c]R-#A#]_qH-%9-<f3^Lon0' );
define( 'WP_CACHE_KEY_SALT', 'EfVv*@>8sC7~{;JT_O-m1.{+Q9Z+p2$Txk<{67ekr&wi([% }X9Qr;f)`y){U@_f' );

// Added at 08/01/2025 for JWT Auth
define('JWT_AUTH_SECRET_KEY', '*]G5]=oq(2Vz*y*kknrO=(5Dr$}b{QHfz6_C0isaKFHweRWkxa;hycg30rcZ-hGL');
define('JWT_AUTH_CORS_ENABLE', true);
/**
 * WordPress Database Table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 */
$table_prefix = 'wp_';
define( 'WP_MEMORY_LIMIT', '512M' );

define( 'ALGOLIA_INDEX_NAME_PREFIX', 'wp_live_' );

define( 'WP_DEBUG_LOG', true );
define( 'WP_DEBUG', true );
define( 'WP_AUTO_UPDATE_CORE', false );
/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', dirname( __FILE__ ) . '/' );
}

/** Sets up WordPress vars and included files. */
require_once ABSPATH . 'wp-settings.php';

